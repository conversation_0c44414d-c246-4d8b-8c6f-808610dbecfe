<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Solar System in a Rotating Cube</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            perspective: 1200px;
        }
        
        .scene {
            position: relative;
            width: 300px;
            height: 300px;
            transform-style: preserve-3d;
            animation: rotateCube 30s infinite linear;
        }
        
        .cube {
            position: relative;
            width: 100%;
            height: 100%;
            transform-style: preserve-3d;
        }
        
        .face {
            position: absolute;
            width: 300px;
            height: 300px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            box-sizing: border-box;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 24px;
            opacity: 0.7;
        }
        
        .front  { transform: translateZ(150px); background: rgba(255, 0, 0, 0.1); }
        .back   { transform: rotateY(180deg) translateZ(150px); background: rgba(0, 255, 0, 0.1); }
        .right  { transform: rotateY(90deg) translateZ(150px); background: rgba(0, 0, 255, 0.1); }
        .left   { transform: rotateY(-90deg) translateZ(150px); background: rgba(255, 255, 0, 0.1); }
        .top    { transform: rotateX(90deg) translateZ(150px); background: rgba(255, 0, 255, 0.1); }
        .bottom { transform: rotateX(-90deg) translateZ(150px); background: rgba(0, 255, 255, 0.1); }
        
        .solar-system {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 200px;
            height: 200px;
        }
        
        .sun {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, #ffd700, #ff8c00);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px #ff8c00;
        }
        
        .planet {
            position: absolute;
            top: 50%;
            left: 50%;
            border-radius: 50%;
            transform-origin: 0 0;
        }
        
        .orbit {
            position: absolute;
            top: 50%;
            left: 50%;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }
        
        .mercury { 
            width: 8px; 
            height: 8px; 
            background: #a9a9a9; 
            animation: orbit 4s infinite linear;
        }
        .venus { 
            width: 12px; 
            height: 12px; 
            background: #ffa500; 
            animation: orbit 8s infinite linear;
        }
        .earth { 
            width: 12px; 
            height: 12px; 
            background: #1e90ff; 
            animation: orbit 12s infinite linear;
        }
        .mars { 
            width: 10px; 
            height: 10px; 
            background: #ff4500; 
            animation: orbit 20s infinite linear;
        }
        
        @keyframes rotateCube {
            0% { transform: rotateX(0) rotateY(0); }
            100% { transform: rotateX(360deg) rotateY(360deg); }
        }
        
        @keyframes orbit {
            0% { transform: rotate(0deg) translateX(50px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(50px) rotate(-360deg); }
        }
    </style>
</head>
<body>
    <div class="scene">
        <div class="cube">
            <div class="face front">Front</div>
            <div class="face back">Back</div>
            <div class="face right">Right</div>
            <div class="face left">Left</div>
            <div class="face top">Top</div>
            <div class="face bottom">Bottom</div>
            
            <div class="solar-system">
                <div class="sun"></div>
                <div class="orbit" style="width: 100px; height: 100px;"></div>
                <div class="orbit" style="width: 130px; height: 130px;"></div>
                <div class="orbit" style="width: 160px; height: 160px;"></div>
                <div class="orbit" style="width: 190px; height: 190px;"></div>
                
                <div class="planet mercury"></div>
                <div class="planet venus"></div>
                <div class="planet earth"></div>
                <div class="planet mars"></div>
            </div>
        </div>
    </div>
</body>
</html>